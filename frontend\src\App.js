import { useEffect, useState } from "react";
import axios from "axios";

function App() {
  const [posts, setPosts] = useState([]);
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");

  // جلب المقالات
  useEffect(() => {
    fetchPosts();
  }, []);

  const fetchPosts = () => {
    axios.get("http://127.0.0.1:8000/api/posts")
      .then(res => setPosts(res.data));
  };

  // إضافة مقال جديد
  const addPost = () => {
    axios.post("http://127.0.0.1:8000/api/posts", { title, content })
      .then(() => {
        fetchPosts();
        setTitle("");
        setContent("");
      });
  };

  // حذف مقال
  const deletePost = (id) => {
    axios.delete(`http://127.0.0.1:8000/api/posts/${id}`)
      .then(() => fetchPosts());
  };

  return (
    <div className="App">
      <h1>Gestion des Articles</h1>

      <input
        type="text"
        placeholder="Titre"
        value={title}
        onChange={e => setTitle(e.target.value)}
      />
      <textarea
        placeholder="Contenu"
        value={content}
        onChange={e => setContent(e.target.value)}
      ></textarea>
      <button onClick={addPost}>Ajouter</button>

      <ul>
        {posts.map(p => (
          <li key={p.id}>
            <h3>{p.title}</h3>
            <p>{p.content}</p>
            <button onClick={() => deletePost(p.id)}>Supprimer</button>
          </li>
        ))}
      </ul>
    </div>
  );
}

export default App;

