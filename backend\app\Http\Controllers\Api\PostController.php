<?php

namespace App\Http\Controllers\Api;

use App\Models\Post;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class PostController extends Controller
{
    public function index() {
        return Post::all();
    }

    public function store(Request $request) {
        $post = Post::create($request->all());
        return response()->json($post, 201);
    }

    public function destroy($id) {
        $post = Post::findOrFail($id);
        $post->delete();
        return response()->json(null, 204);
    }
}
